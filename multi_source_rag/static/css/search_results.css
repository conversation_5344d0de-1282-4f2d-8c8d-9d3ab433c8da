/* Search Results CSS */

:root {
  --primary-color: #4a4a4a;
  --secondary-color: #0366d6;
  --accent-color: #f6f8fa;
  --border-color: #e1e4e8;
  --highlight-color: rgba(255, 255, 0, 0.2);
  --highlight-hover-color: rgba(255, 255, 0, 0.4);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --text-color: #24292e;
  --light-text-color: #586069;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --transition-speed: 0.3s;
}

/* Card styles */
.search-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--transition-speed);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.search-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.search-card-header {
  padding: 1rem;
  background-color: var(--accent-color);
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
}

.search-card-body {
  padding: 1.25rem;
  background-color: #fff;
}

/* Typography */
.response-container {
  line-height: 1.6;
  color: var(--text-color);
}

.response-container p {
  margin-bottom: 1rem;
}

.response-container h1,
.response-container h2,
.response-container h3,
.response-container h4,
.response-container h5,
.response-container h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-color);
}

/* Source items */
.sources-container {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.source-item {
  padding: 0.75rem;
  border-radius: 4px;
  border-left: 3px solid transparent;
  transition: all var(--transition-speed);
  margin-bottom: 0.5rem;
}

.source-item:hover {
  background-color: var(--accent-color);
  border-left-color: var(--secondary-color);
  transform: translateX(3px);
}

.source-item.active {
  background-color: var(--accent-color);
  border-left-color: var(--secondary-color);
}

.source-title {
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.source-content {
  color: var(--text-color);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.source-metadata {
  color: var(--light-text-color);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* Citation and highlight styles */
.citation-highlight {
  background-color: var(--highlight-color);
  border-bottom: 1px dashed var(--warning-color);
  cursor: pointer;
  transition: background-color var(--transition-speed);
  padding: 0 2px;
}

.citation-highlight:hover {
  background-color: var(--highlight-hover-color);
}

.citation-number {
  font-size: 0.8em;
  vertical-align: super;
  color: var(--secondary-color);
  font-weight: bold;
  margin-left: 2px;
  cursor: pointer;
  text-decoration: none;
}

.citation-number:hover {
  text-decoration: underline;
}

/* Metrics display */
.metrics-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5rem;
  gap: 1rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--accent-color);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  min-width: 100px;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--secondary-color);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--light-text-color);
  text-transform: uppercase;
}

/* Relevance score visualization */
.relevance-bar {
  height: 6px;
  border-radius: 3px;
  width: 100%;
  background: linear-gradient(90deg, var(--success-color) 0%, var(--warning-color) 60%, var(--danger-color) 100%);
  position: relative;
  margin-top: 4px;
}

.relevance-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #fff;
  border: 2px solid #333;
  border-radius: 50%;
  top: -1px;
  transform: translateX(-50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Source type badges */
.badge {
  padding: 0.2em 0.6em;
  font-size: 0.75em;
  font-weight: 500;
  border-radius: 10px;
  white-space: nowrap;
}

.source-badge-slack {
  background-color: #4A154B !important;
  color: white;
}

.source-badge-github {
  background-color: #24292E !important;
  color: white;
}

.source-badge-confluence {
  background-color: #0052CC !important;
  color: white;
}

.source-badge-jira {
  background-color: #0052CC !important;
  color: white;
}

.source-badge-google {
  background-color: #4285F4 !important;
  color: white;
}

.source-badge-email {
  background-color: #D14836 !important;
  color: white;
}

.source-badge-web {
  background-color: #607D8B !important;
  color: white;
}

/* Follow-up questions */
.follow-up-container {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.follow-up-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.75rem 0;
}

.follow-up-suggestion {
  background-color: var(--accent-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed);
  white-space: nowrap;
}

.follow-up-suggestion:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

/* Spinner for loading state */
.loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--secondary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced Professional UI Overrides */
.search-card {
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e9ecef !important;
  margin-bottom: 1.5rem !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.search-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
  border-color: #007bff !important;
}

.search-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 2rem !important;
  border-bottom: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.user-query {
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.query-timestamp {
  opacity: 0.9 !important;
  font-size: 0.9rem !important;
  margin-top: 0.5rem !important;
}

.assistant-response {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  border-left: 4px solid #007bff !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
}

.response-content {
  line-height: 1.8 !important;
  margin: 0 !important;
  font-size: 1.05rem !important;
  color: #2c3e50 !important;
}

.confidence-indicator {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.6rem 1.2rem !important;
  border-radius: 25px !important;
  font-size: 0.9rem !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.confidence-high {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
  color: #155724 !important;
  border: 1px solid #c3e6cb !important;
}
.confidence-medium {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  color: #856404 !important;
  border: 1px solid #ffeaa7 !important;
}
.confidence-low {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
  color: #721c24 !important;
  border: 1px solid #f5c6cb !important;
}

.export-options {
  margin-top: 1.5rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid #e9ecef !important;
}

.export-btn {
  margin-right: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.export-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .metrics-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .metric-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }

  .follow-up-suggestions {
    flex-direction: column;
  }

  .follow-up-suggestion {
    width: 100%;
    text-align: center;
  }

  .search-card-header {
    padding: 1.5rem !important;
  }

  .search-card-body {
    padding: 1.5rem !important;
  }

  .assistant-response {
    padding: 1.5rem !important;
  }

  .user-query {
    font-size: 1.1rem !important;
  }
}