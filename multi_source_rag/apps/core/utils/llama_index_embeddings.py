"""
LlamaIndex embedding models integration.

This module provides integration with LlamaIndex embedding models, including:
- HuggingFace embedding models
- Domain-specific embedding models
- Embedding model management
"""

import logging
from typing import Any, Dict, List, Optional, Union

from django.conf import settings
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

from apps.accounts.models import Tenant
from apps.core.models import EmbeddingModel
from apps.core.utils.domain_embeddings import domain_embeddings_manager
from apps.core.utils.llama_index_setup import llama_index_registry

logger = logging.getLogger(__name__)


def initialize_embedding_models() -> None:
    """Initialize and register embedding models with the registry."""
    from apps.core.utils.embedding_consistency import get_consistent_embedding_model, get_embedding_model_info

    # Get the globally consistent embedding model
    try:
        consistent_model = get_consistent_embedding_model()
        model_info = get_embedding_model_info()

        # Register the consistent model as the default
        llama_index_registry.register_embedding("consistent", consistent_model)
        llama_index_registry.set_default_embedding("consistent")

        logger.info(f"Registered consistent embedding model: {model_info['model_type']} ({model_info['dimensions']}d)")

    except Exception as e:
        logger.error(f"Failed to initialize consistent embedding model: {str(e)}")

        # Fallback to HuggingFace if consistent model fails
        default_model_name = getattr(
            settings, "EMBEDDING_MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2"
        )

        # Create and register fallback embedding model
        default_embedding = HuggingFaceEmbedding(
            model_name=default_model_name,
            embed_batch_size=32,
        )
        llama_index_registry.register_embedding("huggingface", default_embedding)
        logger.warning(f"Using fallback HuggingFace embedding: {default_model_name}")

    # Register domain-specific embedding models if available
    for domain, model_info in domain_embeddings_manager.get_available_models().items():
        model_name = model_info.get("model_name")
        if model_name:
            try:
                domain_embedding = HuggingFaceEmbedding(
                    model_name=model_name,
                    embed_batch_size=32,
                )
                llama_index_registry.register_embedding(f"domain_{domain}", domain_embedding)
                logger.info(f"Registered domain-specific embedding model for {domain}: {model_name}")
            except Exception as e:
                logger.error(f"Failed to initialize domain embedding model for {domain}: {str(e)}")

    logger.info(f"Initialized embedding models: {list(llama_index_registry._embeddings.keys())}")


def get_embedding_model_for_content(
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    model_name: Optional[str] = None,
) -> Any:
    """
    Get the appropriate embedding model based on content type, domain, or tenant.
    CRITICAL: Always returns the globally consistent embedding model to prevent dimension mismatches.

    Args:
        content_type: Type of content to embed (for compatibility, but ignored for consistency)
        domain: Domain of content to embed (for compatibility, but ignored for consistency)
        tenant_slug: Tenant slug for tenant-specific models (for compatibility, but ignored for consistency)
        model_name: Specific model name to use (for compatibility, but ignored for consistency)

    Returns:
        Any: LlamaIndex embedding model instance (globally consistent)
    """
    # CRITICAL FIX: Always use the globally consistent embedding model
    # This prevents the dimension mismatch bug that breaks vector search
    from apps.core.utils.embedding_consistency import get_consistent_embedding_model

    try:
        consistent_model = get_consistent_embedding_model()
        logger.debug(f"Using consistent embedding model for content_type={content_type}, domain={domain}")
        return consistent_model
    except Exception as e:
        logger.error(f"Failed to get consistent embedding model: {str(e)}")

        # Emergency fallback to registry default
        try:
            fallback_model = llama_index_registry.get_embedding()
            if fallback_model:
                logger.warning("Using registry fallback embedding model")
                return fallback_model
        except Exception as fallback_error:
            logger.error(f"Registry fallback also failed: {fallback_error}")

        # Last resort: create a basic HuggingFace model
        logger.error("Creating emergency HuggingFace embedding model")
        return HuggingFaceEmbedding(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            embed_batch_size=32,
        )


def get_or_create_embedding_model(
    tenant: Tenant,
    model_name: Optional[str] = None,
    dimension: Optional[int] = None,
) -> EmbeddingModel:
    """
    Get or create an embedding model in the database using consistent configuration.

    Args:
        tenant: Tenant to create model for
        model_name: Name of the embedding model (uses consistent model if None)
        dimension: Dimension of the embedding model (uses consistent dimensions if None)

    Returns:
        EmbeddingModel: Database model for the embedding model
    """
    from apps.core.utils.embedding_consistency import get_embedding_model_info

    # Get consistent model configuration
    model_info = get_embedding_model_info()

    # Use consistent values if not provided
    if model_name is None:
        model_name = model_info.get("model_name", "sentence-transformers/all-MiniLM-L6-v2")
    if dimension is None:
        dimension = model_info.get("dimensions", 384)

    # Get or create embedding model
    embedding_model, created = EmbeddingModel.objects.get_or_create(
        tenant=tenant,
        model_name=model_name,
        defaults={
            "name": f"Embedding Model ({model_name.split('/')[-1]})",
            "dimension": dimension,
            "is_default": True,
        },
    )

    if created:
        logger.info(f"Created consistent embedding model: {embedding_model.name} ({dimension}d)")

    return embedding_model


def convert_to_llama_index_embeddings(
    texts: List[str],
    model_name: Optional[str] = None,
) -> List[List[float]]:
    """
    Convert texts to embeddings using LlamaIndex embedding models.

    Args:
        texts: List of texts to embed
        model_name: Name of the embedding model to use

    Returns:
        List[List[float]]: List of embeddings
    """
    # Get embedding model
    embedding_model = get_embedding_model_for_content(model_name=model_name)

    # Generate embeddings
    embeddings = embedding_model.get_text_embedding_batch(texts)

    return embeddings
