"""
LlamaIndex initialization module.

This module initializes all LlamaIndex components, including:
- Embedding models
- LLMs
- Vector stores
- Node parsers
- Service contexts
- Registry for component management
"""

import logging
from typing import Any, Optional

from django.conf import settings

from llama_index.core import Settings
from llama_index.core.node_parser import Sentence<PERSON>plitter

from apps.core.utils.llama_index_embeddings import initialize_embedding_models
from apps.core.utils.llama_index_llm import initialize_llms
from apps.core.utils.llama_index_vectorstore import initialize_vector_stores

logger = logging.getLogger(__name__)


class LlamaIndexRegistry:
    """
    Registry for LlamaIndex components.

    This class manages all LlamaIndex components, including:
    - Embedding models
    - LLMs
    - Vector stores
    - Node parsers
    - Service contexts
    """

    def __init__(self):
        """Initialize the registry."""
        self._embeddings = {}
        self._llms = {}
        self._vector_stores = {}
        self._node_parsers = {}
        self._service_contexts = {}
        self._default_embedding = None
        self._default_llm = None
        self._default_vector_store = None
        self._default_node_parser = None
        self._default_service_context = None

    # Embedding methods
    def register_embedding(self, name: str, embedding: Any) -> None:
        """Register an embedding model."""
        self._embeddings[name] = embedding
        if self._default_embedding is None:
            self._default_embedding = name

    def get_embedding(self, name: Optional[str] = None) -> Any:
        """Get an embedding model by name or the default."""
        if name is None:
            name = self._default_embedding
        return self._embeddings.get(name)

    def set_default_embedding(self, name: str) -> None:
        """Set the default embedding model."""
        if name in self._embeddings:
            self._default_embedding = name

    # LLM methods
    def register_llm(self, name: str, llm: Any) -> None:
        """Register an LLM."""
        self._llms[name] = llm
        if self._default_llm is None:
            self._default_llm = name

    def get_llm(self, name: Optional[str] = None) -> Any:
        """Get an LLM by name or the default."""
        if name is None:
            name = self._default_llm
        return self._llms.get(name)

    def set_default_llm(self, name: str) -> None:
        """Set the default LLM."""
        if name in self._llms:
            self._default_llm = name

    # Vector store methods
    def register_vector_store(self, name: str, vector_store: Any) -> None:
        """Register a vector store."""
        self._vector_stores[name] = vector_store
        if self._default_vector_store is None:
            self._default_vector_store = name

    def get_vector_store(self, name: Optional[str] = None) -> Any:
        """Get a vector store by name or the default."""
        if name is None:
            name = self._default_vector_store
        return self._vector_stores.get(name)

    def set_default_vector_store(self, name: str) -> None:
        """Set the default vector store."""
        if name in self._vector_stores:
            self._default_vector_store = name

    # Node parser methods
    def register_node_parser(self, name: str, node_parser: Any) -> None:
        """Register a node parser."""
        self._node_parsers[name] = node_parser
        if self._default_node_parser is None:
            self._default_node_parser = name

    def get_node_parser(self, name: Optional[str] = None) -> Any:
        """Get a node parser by name or the default."""
        if name is None:
            name = self._default_node_parser
        return self._node_parsers.get(name)

    def set_default_node_parser(self, name: str) -> None:
        """Set the default node parser."""
        if name in self._node_parsers:
            self._default_node_parser = name

    # Service context methods
    def register_service_context(self, name: str, service_context: Any) -> None:
        """Register a service context."""
        self._service_contexts[name] = service_context
        if self._default_service_context is None:
            self._default_service_context = name

    def get_service_context(self, name: Optional[str] = None) -> Any:
        """Get a service context by name or the default."""
        if name is None:
            name = self._default_service_context
        return self._service_contexts.get(name)

    def set_default_service_context(self, name: str) -> None:
        """Set the default service context."""
        if name in self._service_contexts:
            self._default_service_context = name


# Create global registry instance
llama_index_registry = LlamaIndexRegistry()


def initialize_llama_index(force_reload: bool = False) -> None:
    """
    Initialize all LlamaIndex components.

    Args:
        force_reload: Whether to force reload all components
    """
    # Check if already initialized
    if not force_reload and hasattr(initialize_llama_index, "_initialized") and initialize_llama_index._initialized:
        logger.info("LlamaIndex already initialized, skipping")
        return

    logger.info("Initializing LlamaIndex components")

    try:
        # Initialize embedding models
        initialize_embedding_models()

        # Initialize LLMs
        initialize_llms()

        # Initialize vector stores
        initialize_vector_stores()

        # Initialize node parsers
        _initialize_node_parsers()

        # Initialize service contexts
        _initialize_service_contexts()

        # Mark as initialized
        initialize_llama_index._initialized = True

        logger.info("LlamaIndex components initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing LlamaIndex components: {str(e)}")
        raise


def get_default_node_parser(chunk_size: int = 1000, chunk_overlap: int = 200) -> SentenceSplitter:
    """
    Get a default node parser with specified parameters.

    Args:
        chunk_size: Size of each chunk in characters
        chunk_overlap: Overlap between chunks in characters

    Returns:
        SentenceSplitter instance
    """
    return SentenceSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        paragraph_separator="\n\n",
        secondary_chunking_regex="[^,.;。]+[,.;。]?",
    )


def _initialize_node_parsers() -> None:
    """Initialize and register node parsers with the registry."""
    # Initialize default node parser
    default_parser = get_default_node_parser()
    llama_index_registry.register_node_parser("sentence", default_parser)

    # Initialize node parser with larger chunks
    large_parser = get_default_node_parser(chunk_size=2000, chunk_overlap=400)
    llama_index_registry.register_node_parser("large", large_parser)

    # Initialize node parser with smaller chunks
    small_parser = get_default_node_parser(chunk_size=500, chunk_overlap=100)
    llama_index_registry.register_node_parser("small", small_parser)

    logger.info(f"Initialized node parsers: {list(llama_index_registry._node_parsers.keys())}")


def _initialize_service_contexts() -> None:
    """Initialize and register service contexts with the registry."""
    from llama_index.core import Settings
    from apps.core.utils.embedding_consistency import set_global_embedding_model

    # Initialize global settings with consistent embedding model
    Settings.llm = llama_index_registry.get_llm()

    # Use the consistent embedding model instead of registry default
    set_global_embedding_model()

    Settings.node_parser = llama_index_registry.get_node_parser()

    logger.info("Initialized global settings with consistent embedding model")


def get_llama_index_status() -> dict:
    """
    Get the status of LlamaIndex components.

    Returns:
        dict: Status of LlamaIndex components
    """
    # Check if initialized
    initialized = hasattr(initialize_llama_index, "_initialized") and initialize_llama_index._initialized

    # Get component counts
    llm_count = len(llama_index_registry._llms) if hasattr(llama_index_registry, "_llms") else 0
    embedding_count = len(llama_index_registry._embeddings) if hasattr(llama_index_registry, "_embeddings") else 0
    vector_store_count = len(llama_index_registry._vector_stores) if hasattr(llama_index_registry, "_vector_stores") else 0
    node_parser_count = len(llama_index_registry._node_parsers) if hasattr(llama_index_registry, "_node_parsers") else 0
    service_context_count = len(llama_index_registry._service_contexts) if hasattr(llama_index_registry, "_service_contexts") else 0

    # Get component names
    llm_names = list(llama_index_registry._llms.keys()) if hasattr(llama_index_registry, "_llms") else []
    embedding_names = list(llama_index_registry._embeddings.keys()) if hasattr(llama_index_registry, "_embeddings") else []
    vector_store_names = list(llama_index_registry._vector_stores.keys()) if hasattr(llama_index_registry, "_vector_stores") else []
    node_parser_names = list(llama_index_registry._node_parsers.keys()) if hasattr(llama_index_registry, "_node_parsers") else []
    service_context_names = list(llama_index_registry._service_contexts.keys()) if hasattr(llama_index_registry, "_service_contexts") else []

    return {
        "initialized": initialized,
        "components": {
            "llms": {
                "count": llm_count,
                "names": llm_names,
            },
            "embeddings": {
                "count": embedding_count,
                "names": embedding_names,
            },
            "vector_stores": {
                "count": vector_store_count,
                "names": vector_store_names,
            },
            "node_parsers": {
                "count": node_parser_count,
                "names": node_parser_names,
            },
            "service_contexts": {
                "count": service_context_count,
                "names": service_context_names,
            },
        },
    }
