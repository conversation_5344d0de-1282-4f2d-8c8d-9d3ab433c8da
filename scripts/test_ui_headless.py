#!/usr/bin/env python
"""
Comprehensive UI Testing with Headless Browser

This script tests the enhanced UI with professional design and all functionalities
using Selenium WebDriver in headless mode.

Features tested:
- Search form functionality
- Advanced options
- Loading states
- Search results display
- Responsive design
- Accessibility features

Author: AI Assistant
Date: 2025-01-30
"""

import os
import sys
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_headless_browser():
    """Set up Chrome browser in headless mode."""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Failed to setup Chrome browser: {e}")
        print("Please ensure Chrome and ChromeDriver are installed")
        return None

def test_search_form_ui(driver, base_url):
    """Test the search form UI and functionality."""
    print("🧪 Testing Search Form UI...")
    
    try:
        # Navigate to search page
        driver.get(f"{base_url}/search/")
        wait = WebDriverWait(driver, 10)
        
        # Test hero section
        hero_title = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "hero-title")))
        assert "Multi-Source RAG Search" in hero_title.text
        print("   ✅ Hero section loaded correctly")
        
        # Test search input
        search_input = driver.find_element(By.NAME, "query")
        assert search_input.is_displayed()
        assert search_input.get_attribute("placeholder")
        print("   ✅ Search input is visible and has placeholder")
        
        # Test search button
        search_btn = driver.find_element(By.ID, "searchBtn")
        assert search_btn.is_displayed()
        assert "Search" in search_btn.text
        print("   ✅ Search button is visible")
        
        # Test advanced options toggle
        advanced_toggle = driver.find_element(By.CSS_SELECTOR, "[data-bs-target='#advancedOptions']")
        advanced_toggle.click()
        time.sleep(1)
        
        advanced_panel = driver.find_element(By.ID, "advancedOptions")
        assert "show" in advanced_panel.get_attribute("class")
        print("   ✅ Advanced options panel toggles correctly")
        
        # Test source filters
        source_filters = driver.find_elements(By.CSS_SELECTOR, ".source-filter input[type='checkbox']")
        assert len(source_filters) >= 3
        print(f"   ✅ Found {len(source_filters)} source filter options")
        
        # Test search technique options
        technique_options = driver.find_elements(By.CSS_SELECTOR, "input[name^='use_']")
        assert len(technique_options) >= 4
        print(f"   ✅ Found {len(technique_options)} search technique options")
        
        # Test suggestion chips
        suggestion_chips = driver.find_elements(By.CLASS_NAME, "suggestion-chip")
        assert len(suggestion_chips) >= 5
        print(f"   ✅ Found {len(suggestion_chips)} suggestion chips")
        
        # Test suggestion chip click
        if suggestion_chips:
            first_chip = suggestion_chips[0]
            chip_text = first_chip.get_attribute("data-query")
            first_chip.click()
            time.sleep(0.5)
            
            current_input_value = search_input.get_attribute("value")
            assert current_input_value == chip_text
            print("   ✅ Suggestion chip populates search input correctly")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Search form UI test failed: {e}")
        return False

def test_search_functionality(driver, base_url):
    """Test the search functionality with a real query."""
    print("🧪 Testing Search Functionality...")
    
    try:
        # Navigate to search page
        driver.get(f"{base_url}/search/")
        wait = WebDriverWait(driver, 30)
        
        # Fill in search query
        search_input = wait.until(EC.presence_of_element_located((By.NAME, "query")))
        test_query = "What are the main engineering challenges discussed?"
        search_input.clear()
        search_input.send_keys(test_query)
        print(f"   ✅ Entered test query: '{test_query}'")
        
        # Enable advanced options
        advanced_toggle = driver.find_element(By.CSS_SELECTOR, "[data-bs-target='#advancedOptions']")
        if "show" not in driver.find_element(By.ID, "advancedOptions").get_attribute("class"):
            advanced_toggle.click()
            time.sleep(1)
        
        # Select some advanced options
        hybrid_search = driver.find_element(By.ID, "hybridSearch")
        if not hybrid_search.is_selected():
            hybrid_search.click()
        
        context_aware = driver.find_element(By.ID, "contextAware")
        if not context_aware.is_selected():
            context_aware.click()
        
        print("   ✅ Configured advanced search options")
        
        # Submit search
        search_btn = driver.find_element(By.ID, "searchBtn")
        search_btn.click()
        print("   ✅ Submitted search query")
        
        # Wait for results page (this may take a while for real search)
        try:
            # Check if we're redirected to results page or if there's an error
            wait.until(lambda d: "/search/" in d.current_url and d.current_url != f"{base_url}/search/")
            print("   ✅ Redirected to results page")
            
            # Check for search results elements
            try:
                search_card = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "search-card")))
                print("   ✅ Search results card found")
                
                # Check for query display
                user_query = driver.find_element(By.CLASS_NAME, "user-query")
                assert test_query in user_query.text
                print("   ✅ User query displayed correctly")
                
                # Check for assistant response
                response_content = driver.find_element(By.CLASS_NAME, "response-content")
                assert len(response_content.text) > 50  # Should have substantial content
                print("   ✅ Assistant response found with content")
                
                # Check for sources section
                try:
                    sources_section = driver.find_element(By.CLASS_NAME, "sources-container")
                    source_items = sources_section.find_elements(By.CLASS_NAME, "source-item")
                    print(f"   ✅ Found {len(source_items)} source citations")
                except NoSuchElementException:
                    print("   ⚠️  No sources section found (may be expected if no results)")
                
                return True
                
            except TimeoutException:
                # Check if there's an error message
                try:
                    error_message = driver.find_element(By.CLASS_NAME, "alert-danger")
                    print(f"   ⚠️  Search returned error: {error_message.text}")
                    return False
                except NoSuchElementException:
                    print("   ❌ No search results or error message found")
                    return False
                    
        except TimeoutException:
            print("   ❌ Search request timed out or failed to redirect")
            return False
            
    except Exception as e:
        print(f"   ❌ Search functionality test failed: {e}")
        return False

def test_responsive_design(driver):
    """Test responsive design at different screen sizes."""
    print("🧪 Testing Responsive Design...")
    
    try:
        # Test desktop size
        driver.set_window_size(1920, 1080)
        time.sleep(1)
        
        search_container = driver.find_element(By.CLASS_NAME, "search-container")
        desktop_width = search_container.size['width']
        print(f"   ✅ Desktop layout: container width {desktop_width}px")
        
        # Test tablet size
        driver.set_window_size(768, 1024)
        time.sleep(1)
        
        tablet_width = search_container.size['width']
        print(f"   ✅ Tablet layout: container width {tablet_width}px")
        
        # Test mobile size
        driver.set_window_size(375, 667)
        time.sleep(1)
        
        mobile_width = search_container.size['width']
        print(f"   ✅ Mobile layout: container width {mobile_width}px")
        
        # Verify responsive behavior
        assert mobile_width < tablet_width < desktop_width
        print("   ✅ Responsive design working correctly")
        
        # Reset to desktop size
        driver.set_window_size(1920, 1080)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Responsive design test failed: {e}")
        return False

def test_accessibility_features(driver):
    """Test accessibility features."""
    print("🧪 Testing Accessibility Features...")
    
    try:
        # Test ARIA labels
        search_input = driver.find_element(By.NAME, "query")
        aria_label = search_input.get_attribute("aria-label")
        assert aria_label is not None
        print("   ✅ Search input has ARIA label")
        
        # Test keyboard navigation
        search_input.send_keys(Keys.TAB)
        active_element = driver.switch_to.active_element
        print("   ✅ Keyboard navigation works")
        
        # Test form labels
        labels = driver.find_elements(By.TAG_NAME, "label")
        assert len(labels) > 0
        print(f"   ✅ Found {len(labels)} form labels")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Accessibility test failed: {e}")
        return False

def main():
    """Main function to run all UI tests."""
    print("🎨 Enhanced UI Testing with Headless Browser")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Setup browser
    driver = setup_headless_browser()
    if not driver:
        return 1
    
    try:
        # Test if Django server is running
        driver.get(base_url)
        print("✅ Django server is accessible")
        
        # Run all tests
        tests = [
            ("Search Form UI", lambda: test_search_form_ui(driver, base_url)),
            ("Search Functionality", lambda: test_search_functionality(driver, base_url)),
            ("Responsive Design", lambda: test_responsive_design(driver)),
            ("Accessibility Features", lambda: test_accessibility_features(driver)),
        ]
        
        results = {}
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} Test...")
            try:
                result = test_func()
                results[test_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"   {status}")
            except Exception as e:
                results[test_name] = False
                print(f"   ❌ FAILED: {e}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 UI TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All UI tests passed! The interface is production-ready.")
            return 0
        else:
            print("❌ Some UI tests failed. Please review the issues above.")
            return 1
            
    except Exception as e:
        print(f"❌ Failed to connect to Django server: {e}")
        print("Please ensure the Django server is running on http://localhost:8000")
        return 1
        
    finally:
        driver.quit()

if __name__ == "__main__":
    sys.exit(main())
